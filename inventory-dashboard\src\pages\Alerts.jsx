import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import Card from '../components/Common/Card';
import Loading from '../components/Common/Loading';
import ErrorMessage from '../components/Common/ErrorMessage';
import Badge from '../components/Common/Badge';
import { alertService } from '../services/alertService';
import {
  ExclamationTriangleIcon,
  CheckIcon,
  EyeIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const Alerts = () => {
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState('all');
  const [selectedAlerts, setSelectedAlerts] = useState([]);

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = {};
      if (filter !== 'all') {
        if (['CRITICAL', 'HIGH', 'MEDIUM'].includes(filter)) {
          params.level = filter;
        } else if (['NEW', 'SEEN', 'RESOLVED'].includes(filter)) {
          params.status = filter;
        }
      }
      
      const response = await alertService.getAll(params);
      if (response.success) {
        setAlerts(response.data.alerts || []);
      } else {
        setError('فشل في تحميل التنبيهات');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
      console.error('Alerts error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAlerts();
  }, [filter]);

  const handleResolveAlert = async (alertId) => {
    try {
      await alertService.resolve(alertId, 'تم حل التنبيه من لوحة التحكم');
      fetchAlerts();
    } catch (err) {
      alert('خطأ في حل التنبيه');
    }
  };

  const handleBulkResolve = async () => {
    if (selectedAlerts.length === 0) {
      alert('يرجى اختيار تنبيهات للحل');
      return;
    }

    try {
      await alertService.bulkResolve(selectedAlerts, 'تم حل التنبيهات بشكل جماعي');
      setSelectedAlerts([]);
      fetchAlerts();
    } catch (err) {
      alert('خطأ في حل التنبيهات');
    }
  };

  const handleMarkAsSeen = async (alertId) => {
    try {
      await alertService.markAsSeen(alertId);
      fetchAlerts();
    } catch (err) {
      alert('خطأ في تحديث التنبيه');
    }
  };

  const toggleSelectAlert = (alertId) => {
    setSelectedAlerts(prev => 
      prev.includes(alertId) 
        ? prev.filter(id => id !== alertId)
        : [...prev, alertId]
    );
  };

  const getAlertLevelColor = (level) => {
    switch (level) {
      case 'CRITICAL':
        return 'danger';
      case 'HIGH':
        return 'warning';
      case 'MEDIUM':
        return 'info';
      default:
        return 'default';
    }
  };

  const getAlertLevelText = (level) => {
    switch (level) {
      case 'CRITICAL':
        return 'حرج';
      case 'HIGH':
        return 'عالي';
      case 'MEDIUM':
        return 'متوسط';
      default:
        return level;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'NEW':
        return 'جديد';
      case 'SEEN':
        return 'مشاهد';
      case 'RESOLVED':
        return 'محلول';
      default:
        return status;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'NEW':
        return 'danger';
      case 'SEEN':
        return 'warning';
      case 'RESOLVED':
        return 'success';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Layout title="التنبيهات">
        <Loading size="large" text="جاري تحميل التنبيهات..." />
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="التنبيهات">
        <ErrorMessage message={error} onRetry={fetchAlerts} />
      </Layout>
    );
  }

  return (
    <Layout title="التنبيهات">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">التنبيهات</h2>
            <p className="mt-1 text-sm text-gray-600">
              إدارة تنبيهات المخزون والنظام
            </p>
          </div>
          {selectedAlerts.length > 0 && (
            <button
              onClick={handleBulkResolve}
              className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <CheckIcon className="h-4 w-4 ml-2" />
              حل المحدد ({selectedAlerts.length})
            </button>
          )}
        </div>

        {/* Filters */}
        <Card>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFilter('all')}
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                filter === 'all'
                  ? 'bg-primary-100 text-primary-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              الكل
            </button>
            <button
              onClick={() => setFilter('CRITICAL')}
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                filter === 'CRITICAL'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              حرج
            </button>
            <button
              onClick={() => setFilter('HIGH')}
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                filter === 'HIGH'
                  ? 'bg-orange-100 text-orange-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              عالي
            </button>
            <button
              onClick={() => setFilter('MEDIUM')}
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                filter === 'MEDIUM'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              متوسط
            </button>
            <button
              onClick={() => setFilter('NEW')}
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                filter === 'NEW'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              جديد
            </button>
          </div>
        </Card>

        {/* Alerts List */}
        <div className="space-y-4">
          {alerts.length > 0 ? (
            alerts.map((alert) => (
              <Card key={alert.id} className="hover:shadow-md transition-shadow">
                <div className="flex items-start space-x-4 space-x-reverse">
                  <input
                    type="checkbox"
                    checked={selectedAlerts.includes(alert.id)}
                    onChange={() => toggleSelectAlert(alert.id)}
                    className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                        <h3 className="text-lg font-medium text-gray-900">
                          {alert.product?.name}
                        </h3>
                        <Badge variant={getAlertLevelColor(alert.level)} size="small">
                          {getAlertLevelText(alert.level)}
                        </Badge>
                        <Badge variant={getStatusColor(alert.status)} size="small">
                          {getStatusText(alert.status)}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {alert.status === 'NEW' && (
                          <button
                            onClick={() => handleMarkAsSeen(alert.id)}
                            className="p-1 text-blue-600 hover:text-blue-800"
                            title="تحديد كمشاهد"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        )}
                        {alert.status !== 'RESOLVED' && (
                          <button
                            onClick={() => handleResolveAlert(alert.id)}
                            className="p-1 text-green-600 hover:text-green-800"
                            title="حل التنبيه"
                          >
                            <CheckIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                    
                    <p className="mt-2 text-sm text-gray-600">
                      {alert.alert_message}
                    </p>
                    
                    <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <span>المستودع: {alert.warehouse?.name}</span>
                        <span>الكمية الحالية: {alert.quantity}</span>
                        <span>الحد الأدنى: {alert.minimum_quantity}</span>
                      </div>
                      <span>منذ {alert.age_hours} ساعة</span>
                    </div>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <Card>
              <div className="text-center py-12">
                <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد تنبيهات</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {filter === 'all' ? 'لا توجد تنبيهات في النظام' : `لا توجد تنبيهات ${filter}`}
                </p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Alerts;
