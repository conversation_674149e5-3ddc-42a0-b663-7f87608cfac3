import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import Card from '../components/Common/Card';
import Loading from '../components/Common/Loading';
import ErrorMessage from '../components/Common/ErrorMessage';
import { warehouseService } from '../services/warehouseService';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

const Warehouses = () => {
  const [warehouses, setWarehouses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchWarehouses = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await warehouseService.getAll();
      if (response.success) {
        setWarehouses(response.data.warehouses || []);
      } else {
        setError('فشل في تحميل المستودعات');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWarehouses();
  }, []);

  if (loading) {
    return (
      <Layout title="المستودعات">
        <Loading size="large" text="جاري تحميل المستودعات..." />
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="المستودعات">
        <ErrorMessage message={error} onRetry={fetchWarehouses} />
      </Layout>
    );
  }

  return (
    <Layout title="المستودعات">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">المستودعات</h2>
            <p className="mt-1 text-sm text-gray-600">إدارة جميع المستودعات في النظام</p>
          </div>
          <button className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
            <PlusIcon className="h-4 w-4 ml-2" />
            إضافة مستودع جديد
          </button>
        </div>

        <Card>
          {warehouses.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {warehouses.map((warehouse) => (
                <div key={warehouse.id} className="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">{warehouse.name}</h3>
                    <div className="flex space-x-2 space-x-reverse">
                      <button className="text-indigo-600 hover:text-indigo-900">
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button className="text-red-600 hover:text-red-900">
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p><span className="font-medium">الكود:</span> {warehouse.code}</p>
                    <p><span className="font-medium">الموقع:</span> {warehouse.location || '-'}</p>
                    <p><span className="font-medium">الوصف:</span> {warehouse.description || '-'}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد مستودعات</p>
            </div>
          )}
        </Card>
      </div>
    </Layout>
  );
};

export default Warehouses;
