import api from './api';

export const movementService = {
  // Get all movements
  getAll: async (params = {}) => {
    const response = await api.get('/movements', { params });
    return response.data;
  },

  // Get single movement
  getById: async (id) => {
    const response = await api.get(`/movements/${id}`);
    return response.data;
  },

  // Create new movement
  create: async (movementData) => {
    const response = await api.post('/movements', movementData);
    return response.data;
  },

  // Get movement statistics
  getStats: async () => {
    const response = await api.get('/movements/stats');
    return response.data;
  }
};
