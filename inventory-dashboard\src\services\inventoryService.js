import api from './api';

export const inventoryService = {
  // Get all inventory items
  getAll: async (params = {}) => {
    const response = await api.get('/inventory', { params });
    return response.data;
  },

  // Get single inventory item
  getById: async (id) => {
    const response = await api.get(`/inventory/${id}`);
    return response.data;
  },

  // Update inventory item
  update: async (id, inventoryData) => {
    const response = await api.put(`/inventory/${id}`, inventoryData);
    return response.data;
  },

  // Get inventory statistics
  getStats: async () => {
    const response = await api.get('/inventory/stats');
    return response.data;
  },

  // Get low stock items
  getLowStock: async () => {
    const response = await api.get('/inventory/low-stock');
    return response.data;
  }
};
