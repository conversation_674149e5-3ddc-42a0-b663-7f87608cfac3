import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import Card from '../components/Common/Card';
import Loading from '../components/Common/Loading';
import ErrorMessage from '../components/Common/ErrorMessage';
import Badge from '../components/Common/Badge';
import { reportService } from '../services/reportService';
import {
  CubeIcon,
  BuildingStorefrontIcon,
  BuildingOfficeIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await reportService.getDashboard();
      if (response.success) {
        setDashboardData(response.data);
      } else {
        setError('فشل في تحميل بيانات لوحة التحكم');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
      console.error('Dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <Layout title="لوحة التحكم">
        <Loading size="large" text="جاري تحميل بيانات لوحة التحكم..." />
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="لوحة التحكم">
        <ErrorMessage message={error} onRetry={fetchDashboardData} />
      </Layout>
    );
  }

  const kpis = dashboardData?.kpis || {};
  const salesTrend = dashboardData?.sales_trend || [];
  const topProducts = dashboardData?.top_products || [];
  const inventoryAlerts = dashboardData?.inventory_alerts || {};

  // KPI Cards Data
  const kpiCards = [
    {
      title: 'إجمالي المنتجات',
      value: kpis.inventory?.totalProducts || 0,
      icon: CubeIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'إجمالي الموردين',
      value: kpis.suppliers?.totalSuppliers || 0,
      icon: BuildingStorefrontIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'إجمالي المستودعات',
      value: kpis.inventory?.totalWarehouses || 0,
      icon: BuildingOfficeIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'تنبيهات المخزون',
      value: (inventoryAlerts.low_stock?.count || 0) + (inventoryAlerts.out_of_stock?.count || 0),
      icon: ExclamationTriangleIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    }
  ];

  return (
    <Layout title="لوحة التحكم">
      <div className="space-y-6">
        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {kpiCards.map((kpi, index) => {
            const Icon = kpi.icon;
            return (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <div className="flex items-center">
                  <div className={`p-3 rounded-full ${kpi.bgColor}`}>
                    <Icon className={`h-6 w-6 ${kpi.color}`} />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">{kpi.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{kpi.value.toLocaleString()}</p>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Financial Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card title="المبيعات" className="hover:shadow-md transition-shadow">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">إجمالي المبيعات</span>
                <span className="text-lg font-semibold text-green-600">
                  {(kpis.financial?.totalRevenue || 0).toLocaleString()} ر.س
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">أوامر البيع النشطة</span>
                <span className="text-lg font-semibold text-blue-600">
                  {kpis.orders?.sales?.active || 0}
                </span>
              </div>
            </div>
          </Card>

          <Card title="المشتريات" className="hover:shadow-md transition-shadow">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">إجمالي المشتريات</span>
                <span className="text-lg font-semibold text-orange-600">
                  {(kpis.financial?.totalPurchases || 0).toLocaleString()} ر.س
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">أوامر الشراء النشطة</span>
                <span className="text-lg font-semibold text-blue-600">
                  {kpis.orders?.purchase?.active || 0}
                </span>
              </div>
            </div>
          </Card>

          <Card title="الربح الإجمالي" className="hover:shadow-md transition-shadow">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">الربح/الخسارة</span>
                <div className="flex items-center">
                  {(kpis.financial?.grossProfit || 0) >= 0 ? (
                    <ArrowTrendingUpIcon className="h-5 w-5 text-green-500 ml-1" />
                  ) : (
                    <ArrowTrendingDownIcon className="h-5 w-5 text-red-500 ml-1" />
                  )}
                  <span className={`text-lg font-semibold ${
                    (kpis.financial?.grossProfit || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {(kpis.financial?.grossProfit || 0).toLocaleString()} ر.س
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Top Products and Alerts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Products */}
          <Card title="المنتجات الأكثر مبيعاً" subtitle="أفضل 5 منتجات">
            <div className="space-y-3">
              {topProducts.length > 0 ? (
                topProducts.map((product, index) => (
                  <div key={product.product_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <span className="flex items-center justify-center w-6 h-6 bg-primary-600 text-white text-xs font-bold rounded-full ml-3">
                        {index + 1}
                      </span>
                      <div>
                        <p className="font-medium text-gray-900">{product.product_name}</p>
                        <p className="text-sm text-gray-600">{product.product_code}</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-semibold text-gray-900">{product.total_delivered} {product.unit}</p>
                      <p className="text-sm text-gray-600">{product.total_revenue?.toLocaleString()} ر.س</p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">لا توجد بيانات متاحة</p>
              )}
            </div>
          </Card>

          {/* Inventory Alerts */}
          <Card title="تنبيهات المخزون" subtitle="المنتجات التي تحتاج انتباه">
            <div className="space-y-3">
              {inventoryAlerts.out_of_stock?.items?.slice(0, 3).map((alert) => (
                <div key={alert.inventory_id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                  <div>
                    <p className="font-medium text-gray-900">{alert.product?.name}</p>
                    <p className="text-sm text-gray-600">{alert.warehouse?.name}</p>
                  </div>
                  <Badge variant="danger" size="small">نفاد المخزون</Badge>
                </div>
              ))}
              
              {inventoryAlerts.low_stock?.items?.slice(0, 2).map((alert) => (
                <div key={alert.inventory_id} className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div>
                    <p className="font-medium text-gray-900">{alert.product?.name}</p>
                    <p className="text-sm text-gray-600">الكمية: {alert.current_quantity}</p>
                  </div>
                  <Badge variant="warning" size="small">مخزون منخفض</Badge>
                </div>
              ))}
              
              {(!inventoryAlerts.out_of_stock?.items?.length && !inventoryAlerts.low_stock?.items?.length) && (
                <p className="text-gray-500 text-center py-4">لا توجد تنبيهات</p>
              )}
            </div>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
