import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import Card from '../components/Common/Card';
import Loading from '../components/Common/Loading';
import ErrorMessage from '../components/Common/ErrorMessage';
import Badge from '../components/Common/Badge';
import { inventoryService } from '../services/inventoryService';

const Inventory = () => {
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchInventory = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await inventoryService.getAll();
      if (response.success) {
        setInventory(response.data.inventory || []);
      } else {
        setError('فشل في تحميل الجرد');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInventory();
  }, []);

  const getStockStatus = (quantity, minimumQuantity) => {
    if (quantity === 0) {
      return { text: 'نفاد المخزون', variant: 'danger' };
    } else if (minimumQuantity && quantity <= minimumQuantity) {
      return { text: 'مخزون منخفض', variant: 'warning' };
    } else {
      return { text: 'متوفر', variant: 'success' };
    }
  };

  if (loading) {
    return (
      <Layout title="الجرد">
        <Loading size="large" text="جاري تحميل الجرد..." />
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="الجرد">
        <ErrorMessage message={error} onRetry={fetchInventory} />
      </Layout>
    );
  }

  return (
    <Layout title="الجرد">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">الجرد</h2>
          <p className="mt-1 text-sm text-gray-600">عرض حالة المخزون لجميع المنتجات</p>
        </div>

        <Card>
          {inventory.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المنتج</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستودع</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحد الأدنى</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر فحص</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {inventory.map((item) => {
                    const status = getStockStatus(item.quantity, item.minimum_quantity);
                    return (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {item.product?.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {item.product?.code}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.warehouse?.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.quantity} {item.product?.unit}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.minimum_quantity || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge variant={status.variant} size="small">
                            {status.text}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.last_checked_at ? new Date(item.last_checked_at).toLocaleDateString('ar-SA') : '-'}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد عناصر جرد</p>
            </div>
          )}
        </Card>
      </div>
    </Layout>
  );
};

export default Inventory;
