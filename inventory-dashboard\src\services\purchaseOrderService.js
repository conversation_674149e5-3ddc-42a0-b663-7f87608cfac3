import api from './api';

export const purchaseOrderService = {
  // Get all purchase orders
  getAll: async (params = {}) => {
    const response = await api.get('/purchase-orders', { params });
    return response.data;
  },

  // Get single purchase order
  getById: async (id) => {
    const response = await api.get(`/purchase-orders/${id}`);
    return response.data;
  },

  // Create new purchase order
  create: async (orderData) => {
    const response = await api.post('/purchase-orders', orderData);
    return response.data;
  },

  // Update purchase order
  update: async (id, orderData) => {
    const response = await api.put(`/purchase-orders/${id}`, orderData);
    return response.data;
  },

  // Delete purchase order
  delete: async (id) => {
    const response = await api.delete(`/purchase-orders/${id}`);
    return response.data;
  },

  // Receive products
  receiveProducts: async (id, receiveData) => {
    const response = await api.post(`/purchase-orders/${id}/receive`, receiveData);
    return response.data;
  },

  // Get purchase order statistics
  getStats: async () => {
    const response = await api.get('/purchase-orders/stats');
    return response.data;
  }
};
