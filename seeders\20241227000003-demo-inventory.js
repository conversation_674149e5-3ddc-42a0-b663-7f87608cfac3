'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('inventory', [
      // Warehouse 1 - Main warehouse
      {
        product_id: 1,
        warehouse_id: 1,
        quantity: 15,
        minimum_quantity: 5,
        last_checked_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        product_id: 2,
        warehouse_id: 1,
        quantity: 30,
        minimum_quantity: 10,
        last_checked_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        product_id: 3,
        warehouse_id: 1,
        quantity: 5,
        minimum_quantity: 3,
        last_checked_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      },

      // Warehouse 2 - Jeddah
      {
        product_id: 1,
        warehouse_id: 2,
        quantity: 10,
        minimum_quantity: 3,
        last_checked_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        product_id: 4,
        warehouse_id: 2,
        quantity: 12,
        minimum_quantity: 5,
        last_checked_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      },

      // Warehouse 3 - Dammam
      {
        product_id: 2,
        warehouse_id: 3,
        quantity: 25,
        minimum_quantity: 10,
        last_checked_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        product_id: 5,
        warehouse_id: 3,
        quantity: 8,
        minimum_quantity: 5,
        last_checked_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      },

      // Out of stock examples
      {
        product_id: 3,
        warehouse_id: 2,
        quantity: 0,
        minimum_quantity: 2,
        last_checked_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        product_id: 4,
        warehouse_id: 3,
        quantity: 0,
        minimum_quantity: 3,
        last_checked_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('inventory', null, {});
  }
};
