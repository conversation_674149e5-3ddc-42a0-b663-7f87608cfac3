import api from './api';

export const warehouseService = {
  // Get all warehouses
  getAll: async (params = {}) => {
    const response = await api.get('/warehouses', { params });
    return response.data;
  },

  // Get single warehouse
  getById: async (id) => {
    const response = await api.get(`/warehouses/${id}`);
    return response.data;
  },

  // Create new warehouse
  create: async (warehouseData) => {
    const response = await api.post('/warehouses', warehouseData);
    return response.data;
  },

  // Update warehouse
  update: async (id, warehouseData) => {
    const response = await api.put(`/warehouses/${id}`, warehouseData);
    return response.data;
  },

  // Delete warehouse
  delete: async (id) => {
    const response = await api.delete(`/warehouses/${id}`);
    return response.data;
  },

  // Get warehouse statistics
  getStats: async () => {
    const response = await api.get('/warehouses/stats');
    return response.data;
  }
};
