import React, { useState, useEffect } from 'react';
import {
  Bars3Icon,
  BellIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { alertService } from '../../services/alertService';

const Topbar = ({ toggleSidebar, title = 'لوحة التحكم' }) => {
  const [alerts, setAlerts] = useState([]);
  const [alertCount, setAlertCount] = useState(0);
  const [showAlerts, setShowAlerts] = useState(false);
  const [loading, setLoading] = useState(false);

  // Fetch active alerts
  const fetchAlerts = async () => {
    try {
      setLoading(true);
      const response = await alertService.getActive();
      if (response.success) {
        setAlerts(response.data.alerts || []);
        setAlertCount(response.data.total_count || 0);
      }
    } catch (error) {
      console.error('Error fetching alerts:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAlerts();
    
    // Refresh alerts every 30 seconds
    const interval = setInterval(fetchAlerts, 30000);
    return () => clearInterval(interval);
  }, []);

  const getAlertLevelColor = (level) => {
    switch (level) {
      case 'CRITICAL':
        return 'text-red-600 bg-red-100';
      case 'HIGH':
        return 'text-orange-600 bg-orange-100';
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getAlertLevelText = (level) => {
    switch (level) {
      case 'CRITICAL':
        return 'حرج';
      case 'HIGH':
        return 'عالي';
      case 'MEDIUM':
        return 'متوسط';
      default:
        return level;
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-4 py-3">
        {/* Left side */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* Mobile menu button */}
          <button
            onClick={toggleSidebar}
            className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <Bars3Icon className="h-6 w-6" />
          </button>

          {/* Title */}
          <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* Search */}
          <div className="hidden md:block relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="البحث..."
              className="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm"
            />
          </div>

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowAlerts(!showAlerts)}
              className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <BellIcon className="h-6 w-6" />
              {alertCount > 0 && (
                <span className="absolute -top-1 -left-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                  {alertCount > 99 ? '99+' : alertCount}
                </span>
              )}
            </button>

            {/* Alerts dropdown */}
            {showAlerts && (
              <div className="absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                <div className="py-1">
                  <div className="px-4 py-2 border-b border-gray-200">
                    <h3 className="text-sm font-medium text-gray-900">
                      التنبيهات ({alertCount})
                    </h3>
                  </div>
                  
                  <div className="max-h-64 overflow-y-auto">
                    {loading ? (
                      <div className="px-4 py-3 text-sm text-gray-500 text-center">
                        جاري التحميل...
                      </div>
                    ) : alerts.length > 0 ? (
                      alerts.slice(0, 5).map((alert) => (
                        <div
                          key={alert.id}
                          className="px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                        >
                          <div className="flex items-start space-x-3 space-x-reverse">
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mt-0.5" />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {alert.product?.name}
                              </p>
                              <p className="text-xs text-gray-500 mt-1">
                                {alert.alert_message}
                              </p>
                              <div className="flex items-center mt-2 space-x-2 space-x-reverse">
                                <span
                                  className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getAlertLevelColor(
                                    alert.level
                                  )}`}
                                >
                                  {getAlertLevelText(alert.level)}
                                </span>
                                <span className="text-xs text-gray-400">
                                  منذ {alert.age_hours} ساعة
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="px-4 py-3 text-sm text-gray-500 text-center">
                        لا توجد تنبيهات
                      </div>
                    )}
                  </div>
                  
                  {alerts.length > 0 && (
                    <div className="px-4 py-2 border-t border-gray-200">
                      <button
                        onClick={() => {
                          setShowAlerts(false);
                          window.location.href = '/alerts';
                        }}
                        className="text-sm text-primary-600 hover:text-primary-800 font-medium"
                      >
                        عرض جميع التنبيهات
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* User menu placeholder */}
          <div className="flex items-center">
            <div className="h-8 w-8 bg-primary-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-white">م</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Topbar;
