'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Warehouse extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define association here
      // Future associations with inventory, stock movements, etc.
      // Warehouse.hasMany(models.Inventory, {
      //   foreignKey: 'warehouse_id',
      //   as: 'inventory'
      // });
    }

    // Instance method to get warehouse summary
    getWarehouseSummary() {
      return {
        id: this.id,
        name: this.name,
        code: this.code,
        location: this.location,
        capacity: this.capacity
      };
    }

    // Instance method to check if warehouse has capacity
    hasCapacity(requiredCapacity = 1) {
      if (!this.capacity) return true; // No capacity limit set
      return this.capacity >= requiredCapacity;
    }

    // Static method to find warehouses by location
    static async findByLocation(location) {
      return await this.findAll({
        where: {
          location: {
            [sequelize.Sequelize.Op.iLike]: `%${location}%`
          }
        }
      });
    }

    // Static method to find warehouses with available capacity
    static async findWithCapacity(minCapacity = 0) {
      return await this.findAll({
        where: {
          capacity: {
            [sequelize.Sequelize.Op.gte]: minCapacity
          }
        }
      });
    }

    // Instance method to get location info
    getLocationInfo() {
      return {
        name: this.name,
        code: this.code,
        location: this.location,
        capacity: this.capacity,
        description: this.description
      };
    }
  }

  Warehouse.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'اسم المستودع مطلوب'
        },
        len: {
          args: [2, 255],
          msg: 'اسم المستودع يجب أن يكون بين 2 و 255 حرف'
        }
      }
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: {
        msg: 'كود المستودع يجب أن يكون فريد'
      },
      validate: {
        notEmpty: {
          msg: 'كود المستودع مطلوب'
        },
        len: {
          args: [2, 50],
          msg: 'كود المستودع يجب أن يكون بين 2 و 50 حرف'
        },
        isAlphanumeric: {
          msg: 'كود المستودع يجب أن يحتوي على أحرف وأرقام فقط'
        }
      }
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [2, 255],
          msg: 'الموقع يجب أن يكون بين 2 و 255 حرف'
        }
      }
    },
    capacity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: {
          args: [0],
          msg: 'السعة يجب أن تكون أكبر من أو تساوي 0'
        },
        isInt: {
          msg: 'السعة يجب أن تكون رقم صحيح'
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Warehouse',
    tableName: 'warehouses',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['code']
      },
      {
        fields: ['name']
      },
      {
        fields: ['location']
      },
      {
        fields: ['capacity']
      }
    ]
  });

  return Warehouse;
};
