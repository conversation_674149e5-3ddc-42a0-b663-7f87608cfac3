import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  CubeIcon,
  BuildingStorefrontIcon,
  BuildingOfficeIcon,
  ArchiveBoxIcon,
  ArrowsRightLeftIcon,
  ShoppingCartIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

const Sidebar = ({ isOpen, toggleSidebar }) => {
  const location = useLocation();

  const menuItems = [
    {
      name: 'لوحة التحكم',
      path: '/dashboard',
      icon: HomeIcon,
      color: 'text-blue-600'
    },
    {
      name: 'المنتجات',
      path: '/products',
      icon: CubeIcon,
      color: 'text-green-600'
    },
    {
      name: 'الموردين',
      path: '/suppliers',
      icon: BuildingStorefrontIcon,
      color: 'text-purple-600'
    },
    {
      name: 'المستودعات',
      path: '/warehouses',
      icon: BuildingOfficeIcon,
      color: 'text-indigo-600'
    },
    {
      name: 'الجرد',
      path: '/inventory',
      icon: ArchiveBoxIcon,
      color: 'text-yellow-600'
    },
    {
      name: 'حركة المخزون',
      path: '/movements',
      icon: ArrowsRightLeftIcon,
      color: 'text-cyan-600'
    },
    {
      name: 'أوامر الشراء',
      path: '/purchase-orders',
      icon: ShoppingCartIcon,
      color: 'text-orange-600'
    },
    {
      name: 'أوامر البيع',
      path: '/sales-orders',
      icon: CurrencyDollarIcon,
      color: 'text-emerald-600'
    },
    {
      name: 'التقارير',
      path: '/reports',
      icon: ChartBarIcon,
      color: 'text-pink-600'
    },
    {
      name: 'التنبيهات',
      path: '/alerts',
      icon: ExclamationTriangleIcon,
      color: 'text-red-600'
    }
  ];

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleSidebar}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed top-0 right-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 lg:translate-x-0 lg:static lg:inset-0 ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Logo */}
        <div className="flex items-center justify-center h-16 bg-primary-600 text-white">
          <h1 className="text-xl font-bold">نظام إدارة المخزون</h1>
        </div>

        {/* Navigation */}
        <nav className="mt-8 px-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    onClick={() => window.innerWidth < 1024 && toggleSidebar()}
                    className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      active
                        ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <Icon
                      className={`ml-3 h-5 w-5 ${
                        active ? 'text-primary-600' : item.color
                      }`}
                    />
                    {item.name}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Footer */}
        <div className="absolute bottom-0 w-full p-4 border-t border-gray-200">
          <div className="text-center text-xs text-gray-500">
            <p>نظام إدارة المخزون</p>
            <p className="mt-1">الإصدار 1.0.0</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
