import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import Card from '../components/Common/Card';
import Loading from '../components/Common/Loading';
import ErrorMessage from '../components/Common/ErrorMessage';
import Badge from '../components/Common/Badge';
import { movementService } from '../services/movementService';
import { PlusIcon } from '@heroicons/react/24/outline';

const Movements = () => {
  const [movements, setMovements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchMovements = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await movementService.getAll();
      if (response.success) {
        setMovements(response.data.movements || []);
      } else {
        setError('فشل في تحميل حركة المخزون');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMovements();
  }, []);

  const getMovementTypeText = (type) => {
    return type === 'IN' ? 'دخول' : 'خروج';
  };

  const getMovementTypeVariant = (type) => {
    return type === 'IN' ? 'success' : 'warning';
  };

  if (loading) {
    return (
      <Layout title="حركة المخزون">
        <Loading size="large" text="جاري تحميل حركة المخزون..." />
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="حركة المخزون">
        <ErrorMessage message={error} onRetry={fetchMovements} />
      </Layout>
    );
  }

  return (
    <Layout title="حركة المخزون">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">حركة المخزون</h2>
            <p className="mt-1 text-sm text-gray-600">تتبع جميع حركات الدخول والخروج</p>
          </div>
          <button className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
            <PlusIcon className="h-4 w-4 ml-2" />
            إضافة حركة جديدة
          </button>
        </div>

        <Card>
          {movements.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المنتج</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستودع</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوصف</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {movements.map((movement) => (
                    <tr key={movement.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {movement.product?.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {movement.product?.code}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {movement.warehouse?.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge 
                          variant={getMovementTypeVariant(movement.type)} 
                          size="small"
                        >
                          {getMovementTypeText(movement.type)}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {movement.quantity} {movement.product?.unit}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {movement.description || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(movement.created_at).toLocaleDateString('ar-SA')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد حركات مخزون</p>
            </div>
          )}
        </Card>
      </div>
    </Layout>
  );
};

export default Movements;
