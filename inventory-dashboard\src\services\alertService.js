import api from './api';

export const alertService = {
  // Get all alerts
  getAll: async (params = {}) => {
    const response = await api.get('/alerts', { params });
    return response.data;
  },

  // Get active alerts
  getActive: async () => {
    const response = await api.get('/alerts/active');
    return response.data;
  },

  // Get single alert
  getById: async (id) => {
    const response = await api.get(`/alerts/${id}`);
    return response.data;
  },

  // Mark alert as seen
  markAsSeen: async (id) => {
    const response = await api.put(`/alerts/${id}/seen`);
    return response.data;
  },

  // Resolve alert
  resolve: async (id, resolutionNote = '') => {
    const response = await api.put(`/alerts/${id}/resolve`, {
      resolution_note: resolutionNote
    });
    return response.data;
  },

  // Bulk resolve alerts
  bulkResolve: async (alertIds, resolutionNote = '') => {
    const response = await api.post('/alerts/bulk-resolve', {
      alert_ids: alertIds,
      resolution_note: resolutionNote
    });
    return response.data;
  },

  // Check all inventory for alerts
  checkAll: async () => {
    const response = await api.post('/alerts/check-all');
    return response.data;
  },

  // Get alert statistics
  getStats: async () => {
    const response = await api.get('/alerts/stats');
    return response.data;
  }
};
