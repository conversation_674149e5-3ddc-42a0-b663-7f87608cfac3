const { StockMovement, Product, Warehouse, Inventory, sequelize } = require('../models');
const { Op } = require('sequelize');

// Helper function for error response
const sendErrorResponse = (res, statusCode, message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error.message;
  }
  
  return res.status(statusCode).json(response);
};

// Helper function for success response
const sendSuccessResponse = (res, statusCode, message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

// Get all stock movements
exports.getAllMovements = async (req, res) => {
  try {
    const { page = 1, limit = 10, product_id, warehouse_id, type, start_date, end_date } = req.query;
    const offset = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {};
    
    if (product_id) {
      whereClause.product_id = product_id;
    }
    
    if (warehouse_id) {
      whereClause.warehouse_id = warehouse_id;
    }
    
    if (type) {
      whereClause.type = type;
    }
    
    if (start_date && end_date) {
      whereClause.moved_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      whereClause.moved_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      whereClause.moved_at = {
        [Op.lte]: new Date(end_date)
      };
    }
    
    const { count, rows } = await StockMovement.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'code', 'unit']
        },
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['id', 'name', 'code', 'location']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['moved_at', 'DESC']]
    });
    
    const totalPages = Math.ceil(count / limit);
    
    sendSuccessResponse(res, 200, 'تم جلب حركات المخزون بنجاح', {
      movements: rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error in getAllMovements:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب حركات المخزون', error);
  }
};

// Get single stock movement by ID
exports.getMovementById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const movement = await StockMovement.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'product'
        },
        {
          model: Warehouse,
          as: 'warehouse'
        }
      ]
    });
    
    if (!movement) {
      return sendErrorResponse(res, 404, 'حركة المخزون غير موجودة');
    }
    
    sendSuccessResponse(res, 200, 'تم جلب حركة المخزون بنجاح', { movement });
  } catch (error) {
    console.error('Error in getMovementById:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب حركة المخزون', error);
  }
};

// Create new stock movement
exports.createMovement = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { product_id, warehouse_id, type, quantity, reference, description, moved_at } = req.body;
    
    // Validation
    if (!product_id || !warehouse_id || !type || !quantity) {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'معرف المنتج ومعرف المستودع ونوع الحركة والكمية مطلوبة');
    }
    
    if (!['IN', 'OUT'].includes(type)) {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'نوع الحركة يجب أن يكون IN أو OUT');
    }
    
    if (quantity < 1) {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'الكمية يجب أن تكون أكبر من أو تساوي 1');
    }
    
    // Check if product exists
    const product = await Product.findByPk(product_id);
    if (!product) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'المنتج غير موجود');
    }
    
    // Check if warehouse exists
    const warehouse = await Warehouse.findByPk(warehouse_id);
    if (!warehouse) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'المستودع غير موجود');
    }
    
    // Find or create inventory record
    let [inventory, created] = await Inventory.findOrCreate({
      where: {
        product_id: product_id,
        warehouse_id: warehouse_id
      },
      defaults: {
        quantity: 0,
        last_checked_at: new Date()
      },
      transaction
    });
    
    // Check if OUT movement has sufficient stock
    if (type === 'OUT') {
      if (inventory.quantity < quantity) {
        await transaction.rollback();
        return sendErrorResponse(res, 400, `الكمية المتاحة في المخزون (${inventory.quantity}) أقل من الكمية المطلوبة (${quantity})`);
      }
    }
    
    // Create stock movement
    const movement = await StockMovement.create({
      product_id,
      warehouse_id,
      type,
      quantity,
      reference,
      description,
      moved_at: moved_at || new Date()
    }, { transaction });
    
    // Update inventory quantity
    const newQuantity = type === 'IN' 
      ? inventory.quantity + quantity 
      : inventory.quantity - quantity;
    
    await inventory.update({
      quantity: newQuantity,
      last_checked_at: new Date()
    }, { transaction });
    
    await transaction.commit();
    
    // Fetch the created movement with associations
    const createdMovement = await StockMovement.findByPk(movement.id, {
      include: [
        {
          model: Product,
          as: 'product'
        },
        {
          model: Warehouse,
          as: 'warehouse'
        }
      ]
    });
    
    sendSuccessResponse(res, 201, 'تم إنشاء حركة المخزون بنجاح', { movement: createdMovement });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in createMovement:', error);
    
    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }
    
    sendErrorResponse(res, 500, 'خطأ في إنشاء حركة المخزون', error);
  }
};

// Delete stock movement
exports.deleteMovement = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const movement = await StockMovement.findByPk(id, { transaction });

    if (!movement) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'حركة المخزون غير موجودة');
    }

    // Find inventory record
    const inventory = await Inventory.findOne({
      where: {
        product_id: movement.product_id,
        warehouse_id: movement.warehouse_id
      },
      transaction
    });

    if (inventory) {
      // Reverse the movement effect on inventory
      const newQuantity = movement.type === 'IN'
        ? inventory.quantity - movement.quantity
        : inventory.quantity + movement.quantity;

      // Check if reversing OUT movement would result in negative stock
      if (newQuantity < 0) {
        await transaction.rollback();
        return sendErrorResponse(res, 400, 'لا يمكن حذف هذه الحركة لأنها ستؤدي إلى مخزون سالب');
      }

      await inventory.update({
        quantity: newQuantity,
        last_checked_at: new Date()
      }, { transaction });
    }

    await movement.destroy({ transaction });
    await transaction.commit();

    sendSuccessResponse(res, 200, 'تم حذف حركة المخزون بنجاح');
  } catch (error) {
    await transaction.rollback();
    console.error('Error in deleteMovement:', error);
    sendErrorResponse(res, 500, 'خطأ في حذف حركة المخزون', error);
  }
};

// Get stock movements statistics
exports.getMovementsStats = async (req, res) => {
  try {
    const totalMovements = await StockMovement.count();
    const inMovements = await StockMovement.count({
      where: { type: 'IN' }
    });
    const outMovements = await StockMovement.count({
      where: { type: 'OUT' }
    });

    const totalInQuantity = await StockMovement.sum('quantity', {
      where: { type: 'IN' }
    }) || 0;

    const totalOutQuantity = await StockMovement.sum('quantity', {
      where: { type: 'OUT' }
    }) || 0;

    // Get movements by product
    const movementsByProduct = await StockMovement.findAll({
      attributes: [
        'product_id',
        'type',
        [StockMovement.sequelize.fn('COUNT', StockMovement.sequelize.col('id')), 'movement_count'],
        [StockMovement.sequelize.fn('SUM', StockMovement.sequelize.col('quantity')), 'total_quantity']
      ],
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['name', 'code']
        }
      ],
      group: ['product_id', 'type', 'product.id'],
      order: [[StockMovement.sequelize.fn('SUM', StockMovement.sequelize.col('quantity')), 'DESC']],
      limit: 10
    });

    // Get movements by warehouse
    const movementsByWarehouse = await StockMovement.findAll({
      attributes: [
        'warehouse_id',
        'type',
        [StockMovement.sequelize.fn('COUNT', StockMovement.sequelize.col('id')), 'movement_count'],
        [StockMovement.sequelize.fn('SUM', StockMovement.sequelize.col('quantity')), 'total_quantity']
      ],
      include: [
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['name', 'code']
        }
      ],
      group: ['warehouse_id', 'type', 'warehouse.id'],
      order: [[StockMovement.sequelize.fn('SUM', StockMovement.sequelize.col('quantity')), 'DESC']],
      limit: 10
    });

    // Get recent movements
    const recentMovements = await StockMovement.findAll({
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['name', 'code']
        },
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['name', 'code']
        }
      ],
      order: [['moved_at', 'DESC']],
      limit: 5
    });

    const stats = {
      totalMovements,
      inMovements,
      outMovements,
      totalInQuantity,
      totalOutQuantity,
      netQuantity: totalInQuantity - totalOutQuantity,
      movementsByProduct,
      movementsByWarehouse,
      recentMovements
    };

    sendSuccessResponse(res, 200, 'تم جلب إحصائيات حركات المخزون بنجاح', { stats });
  } catch (error) {
    console.error('Error in getMovementsStats:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب إحصائيات حركات المخزون', error);
  }
};
