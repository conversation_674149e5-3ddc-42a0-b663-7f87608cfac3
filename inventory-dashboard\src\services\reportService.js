import api from './api';

export const reportService = {
  // Get KPIs
  getKPIs: async () => {
    const response = await api.get('/reports/kpis');
    return response.data;
  },

  // Get sales trend
  getSalesTrend: async () => {
    const response = await api.get('/reports/sales-trend');
    return response.data;
  },

  // Get top products
  getTopProducts: async (limit = 5) => {
    const response = await api.get('/reports/top-products', { params: { limit } });
    return response.data;
  },

  // Get top suppliers
  getTopSuppliers: async (limit = 5) => {
    const response = await api.get('/reports/top-suppliers', { params: { limit } });
    return response.data;
  },

  // Get inventory alerts
  getInventoryAlerts: async () => {
    const response = await api.get('/reports/inventory-alerts');
    return response.data;
  },

  // Get orders status
  getOrdersStatus: async () => {
    const response = await api.get('/reports/orders-status');
    return response.data;
  },

  // Get complete dashboard data
  getDashboard: async () => {
    const response = await api.get('/reports/dashboard');
    return response.data;
  }
};
