'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Product extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define association here
      Product.belongsTo(models.Supplier, {
        foreignKey: 'supplier_id',
        as: 'supplier'
      });

      Product.hasMany(models.Inventory, {
        foreignKey: 'product_id',
        as: 'inventory'
      });

      Product.hasMany(models.StockMovement, {
        foreignKey: 'product_id',
        as: 'stockMovements'
      });

      Product.hasMany(models.PurchaseOrderItem, {
        foreignKey: 'product_id',
        as: 'purchaseOrderItems'
      });

      Product.hasMany(models.SalesOrderItem, {
        foreignKey: 'product_id',
        as: 'salesOrderItems'
      });
    }

    // Instance method to get product summary
    getProductSummary() {
      return {
        id: this.id,
        name: this.name,
        code: this.code,
        quantity: this.quantity,
        unit: this.unit
      };
    }

    // Instance method to check if product is in stock
    isInStock() {
      return this.quantity > 0;
    }

    // Static method to find products by supplier
    static async findBySupplier(supplierId) {
      return await this.findAll({
        where: {
          supplier_id: supplierId
        }
      });
    }

    // Static method to find low stock products
    static async findLowStock(threshold = 10) {
      return await this.findAll({
        where: {
          quantity: {
            [sequelize.Sequelize.Op.lte]: threshold
          }
        }
      });
    }
  }

  Product.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'اسم المنتج مطلوب'
        },
        len: {
          args: [2, 255],
          msg: 'اسم المنتج يجب أن يكون بين 2 و 255 حرف'
        }
      }
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: {
        msg: 'كود المنتج يجب أن يكون فريد'
      },
      validate: {
        notEmpty: {
          msg: 'كود المنتج مطلوب'
        },
        len: {
          args: [2, 50],
          msg: 'كود المنتج يجب أن يكون بين 2 و 50 حرف'
        }
      }
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'قطعة',
      validate: {
        len: {
          args: [1, 50],
          msg: 'الوحدة يجب أن تكون بين 1 و 50 حرف'
        }
      }
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: {
          args: [0],
          msg: 'الكمية يجب أن تكون أكبر من أو تساوي 0'
        },
        isInt: {
          msg: 'الكمية يجب أن تكون رقم صحيح'
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    supplier_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Suppliers',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'Product',
    tableName: 'products',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['code']
      },
      {
        fields: ['supplier_id']
      },
      {
        fields: ['quantity']
      }
    ]
  });

  return Product;
};
