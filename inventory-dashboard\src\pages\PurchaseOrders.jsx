import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import Card from '../components/Common/Card';
import Loading from '../components/Common/Loading';
import ErrorMessage from '../components/Common/ErrorMessage';
import Badge from '../components/Common/Badge';
import { purchaseOrderService } from '../services/purchaseOrderService';
import { PlusIcon } from '@heroicons/react/24/outline';

const PurchaseOrders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await purchaseOrderService.getAll();
      if (response.success) {
        setOrders(response.data.orders || []);
      } else {
        setError('فشل في تحميل أوامر الشراء');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  const getStatusText = (status) => {
    const statusMap = {
      'NEW': 'جديد',
      'PARTIALLY_DELIVERED': 'مسلم جزئياً',
      'COMPLETED': 'مكتمل',
      'CANCELLED': 'ملغي'
    };
    return statusMap[status] || status;
  };

  const getStatusVariant = (status) => {
    const variantMap = {
      'NEW': 'info',
      'PARTIALLY_DELIVERED': 'warning',
      'COMPLETED': 'success',
      'CANCELLED': 'danger'
    };
    return variantMap[status] || 'default';
  };

  if (loading) {
    return (
      <Layout title="أوامر الشراء">
        <Loading size="large" text="جاري تحميل أوامر الشراء..." />
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="أوامر الشراء">
        <ErrorMessage message={error} onRetry={fetchOrders} />
      </Layout>
    );
  }

  return (
    <Layout title="أوامر الشراء">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">أوامر الشراء</h2>
            <p className="mt-1 text-sm text-gray-600">إدارة جميع أوامر الشراء</p>
          </div>
          <button className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
            <PlusIcon className="h-4 w-4 ml-2" />
            إضافة أمر شراء جديد
          </button>
        </div>

        <Card>
          {orders.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الأمر</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المورد</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ الإجمالي</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الطلب</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ التسليم</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {order.order_number}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.supplier?.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge 
                          variant={getStatusVariant(order.status)} 
                          size="small"
                        >
                          {getStatusText(order.status)}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.total_amount ? `${order.total_amount.toLocaleString()} ر.س` : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(order.order_date).toLocaleDateString('ar-SA')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {order.expected_delivery_date 
                          ? new Date(order.expected_delivery_date).toLocaleDateString('ar-SA')
                          : '-'
                        }
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد أوامر شراء</p>
            </div>
          )}
        </Card>
      </div>
    </Layout>
  );
};

export default PurchaseOrders;
