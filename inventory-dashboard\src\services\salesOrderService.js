import api from './api';

export const salesOrderService = {
  // Get all sales orders
  getAll: async (params = {}) => {
    const response = await api.get('/sales-orders', { params });
    return response.data;
  },

  // Get single sales order
  getById: async (id) => {
    const response = await api.get(`/sales-orders/${id}`);
    return response.data;
  },

  // Create new sales order
  create: async (orderData) => {
    const response = await api.post('/sales-orders', orderData);
    return response.data;
  },

  // Update sales order
  update: async (id, orderData) => {
    const response = await api.put(`/sales-orders/${id}`, orderData);
    return response.data;
  },

  // Delete sales order
  delete: async (id) => {
    const response = await api.delete(`/sales-orders/${id}`);
    return response.data;
  },

  // Deliver products
  deliverProducts: async (id, deliverData) => {
    const response = await api.post(`/sales-orders/${id}/deliver`, deliverData);
    return response.data;
  },

  // Get sales order statistics
  getStats: async () => {
    const response = await api.get('/sales-orders/stats');
    return response.data;
  }
};
