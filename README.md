# Product Management Module

وحدة إدارة المنتجات المتكاملة باستخدام Node.js, Express.js, Sequelize ORM, و PostgreSQL.

## المتطلبات

- Node.js (v14 أو أحدث)
- PostgreSQL (v12 أو أحدث)
- npm أو yarn

## التثبيت والإعداد

### 1. تثبيت التبعيات

```bash
npm install
```

### 2. إعداد قاعدة البيانات

1. إنشاء قاعدة بيانات PostgreSQL جديدة:
```sql
CREATE DATABASE product_management_db;
```

2. تحديث ملف `.env` بمعلومات قاعدة البيانات الخاصة بك:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=product_management_db
DB_USER=postgres
DB_PASSWORD=your_password_here
PORT=3000
NODE_ENV=development
```

### 3. تشغيل الهجرات

```bash
npm run migrate
```

### 4. تشغيل التطبيق

```bash
# للتطوير (مع إعادة التشغيل التلقائي)
npm run dev

# للإنتاج
npm start
```

## API Endpoints

### المنتجات (Products)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/products` | جلب كل المنتجات |
| GET | `/api/products/:id` | جلب منتج واحد |
| POST | `/api/products` | إضافة منتج جديد |
| PUT | `/api/products/:id` | تحديث منتج |
| DELETE | `/api/products/:id` | حذف منتج |
| GET | `/api/products/stats` | إحصائيات المنتجات |

### الموردين (Suppliers)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/suppliers` | جلب كل الموردين |
| GET | `/api/suppliers/:id` | جلب مورد واحد |
| POST | `/api/suppliers` | إضافة مورد جديد |
| PUT | `/api/suppliers/:id` | تحديث مورد |
| DELETE | `/api/suppliers/:id` | حذف مورد |
| GET | `/api/suppliers/stats` | إحصائيات الموردين |

### المستودعات (Warehouses)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/warehouses` | جلب كل المستودعات |
| GET | `/api/warehouses/:id` | جلب مستودع واحد |
| POST | `/api/warehouses` | إضافة مستودع جديد |
| PUT | `/api/warehouses/:id` | تحديث مستودع |
| DELETE | `/api/warehouses/:id` | حذف مستودع |
| GET | `/api/warehouses/stats` | إحصائيات المستودعات |

### الجرد (Inventory)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/inventory` | جلب كل سجلات الجرد |
| GET | `/api/inventory/:id` | جلب سجل جرد واحد |
| POST | `/api/inventory` | إضافة سجل جرد جديد |
| PUT | `/api/inventory/:id` | تحديث سجل جرد |
| DELETE | `/api/inventory/:id` | حذف سجل جرد |
| GET | `/api/inventory/stats` | إحصائيات الجرد |

### حركة المخزون (Stock Movements)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/movements` | جلب كل حركات المخزون |
| GET | `/api/movements/:id` | جلب حركة مخزون واحدة |
| POST | `/api/movements` | إضافة حركة مخزون جديدة |
| DELETE | `/api/movements/:id` | حذف حركة مخزون |
| GET | `/api/movements/stats` | إحصائيات حركة المخزون |

### أوامر الشراء (Purchase Orders)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/purchase-orders` | جلب كل أوامر الشراء |
| GET | `/api/purchase-orders/:id` | جلب أمر شراء واحد |
| POST | `/api/purchase-orders` | إنشاء أمر شراء جديد |
| PUT | `/api/purchase-orders/:id` | تحديث أمر شراء |
| DELETE | `/api/purchase-orders/:id` | حذف أمر شراء |
| POST | `/api/purchase-orders/:id/receive` | استلام منتجات |
| GET | `/api/purchase-orders/stats` | إحصائيات أوامر الشراء |

### أوامر البيع (Sales Orders)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/sales-orders` | جلب كل أوامر البيع |
| GET | `/api/sales-orders/:id` | جلب أمر بيع واحد |
| POST | `/api/sales-orders` | إنشاء أمر بيع جديد |
| PUT | `/api/sales-orders/:id` | تحديث أمر بيع |
| DELETE | `/api/sales-orders/:id` | حذف أمر بيع |
| POST | `/api/sales-orders/:id/deliver` | تسليم منتجات |
| GET | `/api/sales-orders/stats` | إحصائيات أوامر البيع |

### التقارير ولوحة التحكم (Reports & Dashboard)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/reports/kpis` | المؤشرات الرئيسية للأداء |
| GET | `/api/reports/sales-trend` | اتجاه المبيعات (6 أشهر) |
| GET | `/api/reports/top-products` | المنتجات الأكثر مبيعاً |
| GET | `/api/reports/top-suppliers` | الموردين الأعلى توريداً |
| GET | `/api/reports/inventory-alerts` | تنبيهات المخزون |
| GET | `/api/reports/orders-status` | حالة الطلبات |
| GET | `/api/reports/dashboard` | لوحة التحكم الكاملة |

### معاملات البحث والتصفية

#### للمنتجات:
- `?page=1&limit=10` - التصفح
- `?search=keyword` - البحث في الاسم والكود والوصف
- `?supplier_id=1` - تصفية حسب المورد
- `?low_stock=10` - المنتجات قليلة المخزون

#### للموردين:
- `?page=1&limit=10` - التصفح
- `?search=keyword` - البحث في الاسم ومسؤول التواصل والبريد والرقم الضريبي
- `?include_products=true` - تضمين المنتجات المرتبطة بالمورد

#### للمستودعات:
- `?page=1&limit=10` - التصفح
- `?search=keyword` - البحث في الاسم والكود والموقع والوصف
- `?min_capacity=1000` - المستودعات بسعة أكبر من أو تساوي القيمة المحددة

#### للجرد:
- `?page=1&limit=10` - التصفح
- `?product_id=1` - تصفية حسب المنتج
- `?warehouse_id=1` - تصفية حسب المستودع
- `?low_stock=true` - المنتجات قليلة المخزون
- `?out_of_stock=true` - المنتجات نافدة المخزون

#### لحركة المخزون:
- `?page=1&limit=10` - التصفح
- `?product_id=1` - تصفية حسب المنتج
- `?warehouse_id=1` - تصفية حسب المستودع
- `?type=IN` أو `?type=OUT` - تصفية حسب نوع الحركة
- `?start_date=2024-01-01&end_date=2024-12-31` - تصفية حسب التاريخ

#### لأوامر الشراء:
- `?page=1&limit=10` - التصفح
- `?supplier_id=1` - تصفية حسب المورد
- `?status=NEW` - تصفية حسب الحالة (NEW, PARTIALLY_DELIVERED, COMPLETED, CANCELLED)
- `?start_date=2024-01-01&end_date=2024-12-31` - تصفية حسب تاريخ الطلب

#### لأوامر البيع:
- `?page=1&limit=10` - التصفح
- `?customer_name=أحمد` - تصفية حسب اسم العميل
- `?status=NEW` - تصفية حسب الحالة (NEW, PARTIALLY_DELIVERED, COMPLETED, CANCELLED)
- `?start_date=2024-01-01&end_date=2024-12-31` - تصفية حسب تاريخ الطلب

#### للتقارير:
- `?limit=5` - الحد الأقصى للنتائج (للمنتجات والموردين الأعلى)

## هيكل البيانات

### Product Model

```json
{
  "id": "integer (auto increment)",
  "name": "string (required)",
  "code": "string (required, unique)",
  "unit": "string (default: 'قطعة')",
  "quantity": "integer (default: 0, >= 0)",
  "description": "text (optional)",
  "supplier_id": "integer (foreign key, optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### Supplier Model

```json
{
  "id": "integer (auto increment)",
  "name": "string (required)",
  "contact_name": "string (optional)",
  "phone": "string (optional, 7-20 digits)",
  "email": "string (optional, valid email format)",
  "tax_number": "string (optional, unique)",
  "address": "text (optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### Warehouse Model

```json
{
  "id": "integer (auto increment)",
  "name": "string (required)",
  "code": "string (required, unique, alphanumeric)",
  "location": "string (optional)",
  "capacity": "integer (optional, >= 0)",
  "description": "text (optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### Inventory Model

```json
{
  "id": "integer (auto increment)",
  "product_id": "integer (required, foreign key)",
  "warehouse_id": "integer (required, foreign key)",
  "quantity": "integer (required, >= 0)",
  "minimum_quantity": "integer (optional, >= 0)",
  "last_checked_at": "timestamp (optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### StockMovement Model

```json
{
  "id": "integer (auto increment)",
  "product_id": "integer (required, foreign key)",
  "warehouse_id": "integer (required, foreign key)",
  "type": "enum ('IN', 'OUT') (required)",
  "quantity": "integer (required, >= 1)",
  "reference": "string (optional)",
  "description": "text (optional)",
  "moved_at": "timestamp (default: now)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### PurchaseOrder Model

```json
{
  "id": "integer (auto increment)",
  "supplier_id": "integer (required, foreign key)",
  "order_number": "string (required, unique)",
  "status": "enum ('NEW', 'PARTIALLY_DELIVERED', 'COMPLETED', 'CANCELLED') (required)",
  "total_amount": "decimal (optional)",
  "order_date": "timestamp (default: now)",
  "delivery_date": "timestamp (optional)",
  "notes": "text (optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### PurchaseOrderItem Model

```json
{
  "id": "integer (auto increment)",
  "purchase_order_id": "integer (required, foreign key)",
  "product_id": "integer (required, foreign key)",
  "quantity_ordered": "integer (required, >= 1)",
  "quantity_received": "integer (default: 0, >= 0)",
  "unit_price": "decimal (optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### SalesOrder Model

```json
{
  "id": "integer (auto increment)",
  "order_number": "string (required, unique, auto-generated)",
  "customer_name": "string (required)",
  "customer_phone": "string (optional)",
  "status": "enum ('NEW', 'PARTIALLY_DELIVERED', 'COMPLETED', 'CANCELLED') (required)",
  "total_amount": "decimal (optional, calculated)",
  "order_date": "timestamp (default: now)",
  "delivery_date": "timestamp (optional)",
  "notes": "text (optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### SalesOrderItem Model

```json
{
  "id": "integer (auto increment)",
  "sales_order_id": "integer (required, foreign key)",
  "product_id": "integer (required, foreign key)",
  "quantity_ordered": "integer (required, >= 1)",
  "quantity_delivered": "integer (default: 0, >= 0)",
  "unit_price": "decimal (optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

## أمثلة للاختبار مع Postman

### 1. إضافة منتج جديد (POST /api/products)

```json
{
  "name": "لابتوب ديل",
  "code": "DELL-LAP-001",
  "unit": "قطعة",
  "quantity": 50,
  "description": "لابتوب ديل انسبايرون 15 بوصة",
  "supplier_id": null
}
```

### 2. تحديث منتج (PUT /api/products/1)

```json
{
  "name": "لابتوب ديل محدث",
  "quantity": 45,
  "description": "لابتوب ديل انسبايرون 15 بوصة - محدث"
}
```

### 3. جلب المنتجات مع التصفية (GET)

```
GET /api/products?page=1&limit=5&search=لابتوب
GET /api/products?low_stock=10
GET /api/products/stats
```

## الاستجابات

### استجابة ناجحة

```json
{
  "success": true,
  "message": "تم جلب المنتجات بنجاح",
  "timestamp": "2024-12-27T10:30:00.000Z",
  "data": {
    "products": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "itemsPerPage": 10
    }
  }
}
```

### استجابة خطأ

```json
{
  "success": false,
  "message": "المنتج غير موجود",
  "timestamp": "2024-12-27T10:30:00.000Z"
}
```

## الميزات

- ✅ CRUD كامل للمنتجات
- ✅ التحقق من صحة البيانات
- ✅ البحث والتصفية
- ✅ التصفح (Pagination)
- ✅ إحصائيات المنتجات
- ✅ معالجة الأخطاء
- ✅ تسجيل العمليات
- ✅ دعم CORS
- ✅ فحص صحة الخادم

## الأوامر المفيدة

```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق للتطوير
npm run dev

# تشغيل الهجرات
npm run migrate

# التراجع عن الهجرة الأخيرة
npm run migrate:undo
```

## البنية

```
├── config/
│   └── database.js          # إعدادات قاعدة البيانات
├── controllers/
│   └── product.controller.js # منطق المنتجات
├── migrations/
│   └── 20241227000001-create-product.js # هجرة المنتجات
├── models/
│   ├── index.js             # إعداد Sequelize
│   └── product.js           # نموذج المنتج
├── routes/
│   └── product.routes.js    # مسارات المنتجات
├── .env                     # متغيرات البيئة
├── .sequelizerc            # إعدادات Sequelize CLI
├── app.js                  # التطبيق الرئيسي
└── package.json            # التبعيات والسكريبتات
```
