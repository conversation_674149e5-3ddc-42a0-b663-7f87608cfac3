# Product Management Module

وحدة إدارة المنتجات المتكاملة باستخدام Node.js, Express.js, Sequelize ORM, و PostgreSQL.

## المتطلبات

- Node.js (v14 أو أحدث)
- PostgreSQL (v12 أو أحدث)
- npm أو yarn

## التثبيت والإعداد

### 1. تثبيت التبعيات

```bash
npm install
```

### 2. إعداد قاعدة البيانات

1. إنشاء قاعدة بيانات PostgreSQL جديدة:
```sql
CREATE DATABASE product_management_db;
```

2. تحديث ملف `.env` بمعلومات قاعدة البيانات الخاصة بك:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=product_management_db
DB_USER=postgres
DB_PASSWORD=your_password_here
PORT=3000
NODE_ENV=development
```

### 3. تشغيل الهجرات

```bash
npm run migrate
```

### 4. تشغيل التطبيق

```bash
# للتطوير (مع إعادة التشغيل التلقائي)
npm run dev

# للإنتاج
npm start
```

## API Endpoints

### المنتجات (Products)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/products` | جلب كل المنتجات |
| GET | `/api/products/:id` | جلب منتج واحد |
| POST | `/api/products` | إضافة منتج جديد |
| PUT | `/api/products/:id` | تحديث منتج |
| DELETE | `/api/products/:id` | حذف منتج |
| GET | `/api/products/stats` | إحصائيات المنتجات |

### الموردين (Suppliers)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/suppliers` | جلب كل الموردين |
| GET | `/api/suppliers/:id` | جلب مورد واحد |
| POST | `/api/suppliers` | إضافة مورد جديد |
| PUT | `/api/suppliers/:id` | تحديث مورد |
| DELETE | `/api/suppliers/:id` | حذف مورد |
| GET | `/api/suppliers/stats` | إحصائيات الموردين |

### المستودعات (Warehouses)

| Method | Endpoint | الوصف |
|--------|----------|--------|
| GET | `/api/warehouses` | جلب كل المستودعات |
| GET | `/api/warehouses/:id` | جلب مستودع واحد |
| POST | `/api/warehouses` | إضافة مستودع جديد |
| PUT | `/api/warehouses/:id` | تحديث مستودع |
| DELETE | `/api/warehouses/:id` | حذف مستودع |
| GET | `/api/warehouses/stats` | إحصائيات المستودعات |

### معاملات البحث والتصفية

#### للمنتجات:
- `?page=1&limit=10` - التصفح
- `?search=keyword` - البحث في الاسم والكود والوصف
- `?supplier_id=1` - تصفية حسب المورد
- `?low_stock=10` - المنتجات قليلة المخزون

#### للموردين:
- `?page=1&limit=10` - التصفح
- `?search=keyword` - البحث في الاسم ومسؤول التواصل والبريد والرقم الضريبي
- `?include_products=true` - تضمين المنتجات المرتبطة بالمورد

#### للمستودعات:
- `?page=1&limit=10` - التصفح
- `?search=keyword` - البحث في الاسم والكود والموقع والوصف
- `?min_capacity=1000` - المستودعات بسعة أكبر من أو تساوي القيمة المحددة

## هيكل البيانات

### Product Model

```json
{
  "id": "integer (auto increment)",
  "name": "string (required)",
  "code": "string (required, unique)",
  "unit": "string (default: 'قطعة')",
  "quantity": "integer (default: 0, >= 0)",
  "description": "text (optional)",
  "supplier_id": "integer (foreign key, optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### Supplier Model

```json
{
  "id": "integer (auto increment)",
  "name": "string (required)",
  "contact_name": "string (optional)",
  "phone": "string (optional, 7-20 digits)",
  "email": "string (optional, valid email format)",
  "tax_number": "string (optional, unique)",
  "address": "text (optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### Warehouse Model

```json
{
  "id": "integer (auto increment)",
  "name": "string (required)",
  "code": "string (required, unique, alphanumeric)",
  "location": "string (optional)",
  "capacity": "integer (optional, >= 0)",
  "description": "text (optional)",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

## أمثلة للاختبار مع Postman

### 1. إضافة منتج جديد (POST /api/products)

```json
{
  "name": "لابتوب ديل",
  "code": "DELL-LAP-001",
  "unit": "قطعة",
  "quantity": 50,
  "description": "لابتوب ديل انسبايرون 15 بوصة",
  "supplier_id": null
}
```

### 2. تحديث منتج (PUT /api/products/1)

```json
{
  "name": "لابتوب ديل محدث",
  "quantity": 45,
  "description": "لابتوب ديل انسبايرون 15 بوصة - محدث"
}
```

### 3. جلب المنتجات مع التصفية (GET)

```
GET /api/products?page=1&limit=5&search=لابتوب
GET /api/products?low_stock=10
GET /api/products/stats
```

## الاستجابات

### استجابة ناجحة

```json
{
  "success": true,
  "message": "تم جلب المنتجات بنجاح",
  "timestamp": "2024-12-27T10:30:00.000Z",
  "data": {
    "products": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "itemsPerPage": 10
    }
  }
}
```

### استجابة خطأ

```json
{
  "success": false,
  "message": "المنتج غير موجود",
  "timestamp": "2024-12-27T10:30:00.000Z"
}
```

## الميزات

- ✅ CRUD كامل للمنتجات
- ✅ التحقق من صحة البيانات
- ✅ البحث والتصفية
- ✅ التصفح (Pagination)
- ✅ إحصائيات المنتجات
- ✅ معالجة الأخطاء
- ✅ تسجيل العمليات
- ✅ دعم CORS
- ✅ فحص صحة الخادم

## الأوامر المفيدة

```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق للتطوير
npm run dev

# تشغيل الهجرات
npm run migrate

# التراجع عن الهجرة الأخيرة
npm run migrate:undo
```

## البنية

```
├── config/
│   └── database.js          # إعدادات قاعدة البيانات
├── controllers/
│   └── product.controller.js # منطق المنتجات
├── migrations/
│   └── 20241227000001-create-product.js # هجرة المنتجات
├── models/
│   ├── index.js             # إعداد Sequelize
│   └── product.js           # نموذج المنتج
├── routes/
│   └── product.routes.js    # مسارات المنتجات
├── .env                     # متغيرات البيئة
├── .sequelizerc            # إعدادات Sequelize CLI
├── app.js                  # التطبيق الرئيسي
└── package.json            # التبعيات والسكريبتات
```
