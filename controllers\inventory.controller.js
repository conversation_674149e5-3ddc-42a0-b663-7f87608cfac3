const { Inventory, Product, Warehouse } = require('../models');
const { Op } = require('sequelize');

// Helper function for error response
const sendErrorResponse = (res, statusCode, message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error.message;
  }
  
  return res.status(statusCode).json(response);
};

// Helper function for success response
const sendSuccessResponse = (res, statusCode, message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

// Get all inventory records
exports.getAllInventory = async (req, res) => {
  try {
    const { page = 1, limit = 10, product_id, warehouse_id, low_stock, out_of_stock } = req.query;
    const offset = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {};
    
    if (product_id) {
      whereClause.product_id = product_id;
    }
    
    if (warehouse_id) {
      whereClause.warehouse_id = warehouse_id;
    }
    
    if (low_stock === 'true') {
      whereClause[Op.and] = [
        { minimum_quantity: { [Op.ne]: null } },
        { [Op.where]: { [Op.col]: 'quantity' }, [Op.lte]: { [Op.col]: 'minimum_quantity' } }
      ];
    }
    
    if (out_of_stock === 'true') {
      whereClause.quantity = 0;
    }
    
    const { count, rows } = await Inventory.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'code', 'unit']
        },
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['id', 'name', 'code', 'location']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['updated_at', 'DESC']]
    });
    
    const totalPages = Math.ceil(count / limit);
    
    sendSuccessResponse(res, 200, 'تم جلب سجلات الجرد بنجاح', {
      inventory: rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error in getAllInventory:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب سجلات الجرد', error);
  }
};

// Get single inventory record by ID
exports.getInventoryById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const inventory = await Inventory.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'product'
        },
        {
          model: Warehouse,
          as: 'warehouse'
        }
      ]
    });
    
    if (!inventory) {
      return sendErrorResponse(res, 404, 'سجل الجرد غير موجود');
    }
    
    sendSuccessResponse(res, 200, 'تم جلب سجل الجرد بنجاح', { inventory });
  } catch (error) {
    console.error('Error in getInventoryById:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب سجل الجرد', error);
  }
};

// Create new inventory record
exports.createInventory = async (req, res) => {
  try {
    const { product_id, warehouse_id, quantity, minimum_quantity, last_checked_at } = req.body;
    
    // Validation
    if (!product_id || !warehouse_id) {
      return sendErrorResponse(res, 400, 'معرف المنتج ومعرف المستودع مطلوبان');
    }
    
    if (quantity !== undefined && quantity < 0) {
      return sendErrorResponse(res, 400, 'الكمية يجب أن تكون أكبر من أو تساوي 0');
    }
    
    if (minimum_quantity !== undefined && minimum_quantity < 0) {
      return sendErrorResponse(res, 400, 'الحد الأدنى للكمية يجب أن يكون أكبر من أو يساوي 0');
    }
    
    // Check if product exists
    const product = await Product.findByPk(product_id);
    if (!product) {
      return sendErrorResponse(res, 404, 'المنتج غير موجود');
    }
    
    // Check if warehouse exists
    const warehouse = await Warehouse.findByPk(warehouse_id);
    if (!warehouse) {
      return sendErrorResponse(res, 404, 'المستودع غير موجود');
    }
    
    const inventory = await Inventory.create({
      product_id,
      warehouse_id,
      quantity: quantity || 0,
      minimum_quantity,
      last_checked_at: last_checked_at || new Date()
    });
    
    // Fetch the created record with associations
    const createdInventory = await Inventory.findByPk(inventory.id, {
      include: [
        {
          model: Product,
          as: 'product'
        },
        {
          model: Warehouse,
          as: 'warehouse'
        }
      ]
    });
    
    sendSuccessResponse(res, 201, 'تم إنشاء سجل الجرد بنجاح', { inventory: createdInventory });
  } catch (error) {
    console.error('Error in createInventory:', error);
    
    // Handle unique constraint error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return sendErrorResponse(res, 400, 'يوجد سجل جرد مسبق لهذا المنتج في هذا المستودع');
    }
    
    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }
    
    sendErrorResponse(res, 500, 'خطأ في إنشاء سجل الجرد', error);
  }
};

// Update inventory record
exports.updateInventory = async (req, res) => {
  try {
    const { id } = req.params;
    const { quantity, minimum_quantity, last_checked_at } = req.body;

    const inventory = await Inventory.findByPk(id);

    if (!inventory) {
      return sendErrorResponse(res, 404, 'سجل الجرد غير موجود');
    }

    // Validation
    if (quantity !== undefined && quantity < 0) {
      return sendErrorResponse(res, 400, 'الكمية يجب أن تكون أكبر من أو تساوي 0');
    }

    if (minimum_quantity !== undefined && minimum_quantity < 0) {
      return sendErrorResponse(res, 400, 'الحد الأدنى للكمية يجب أن يكون أكبر من أو يساوي 0');
    }

    // Update inventory
    await inventory.update({
      quantity: quantity !== undefined ? quantity : inventory.quantity,
      minimum_quantity: minimum_quantity !== undefined ? minimum_quantity : inventory.minimum_quantity,
      last_checked_at: last_checked_at !== undefined ? last_checked_at : new Date()
    });

    // Fetch updated record with associations
    const updatedInventory = await Inventory.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'product'
        },
        {
          model: Warehouse,
          as: 'warehouse'
        }
      ]
    });

    sendSuccessResponse(res, 200, 'تم تحديث سجل الجرد بنجاح', { inventory: updatedInventory });
  } catch (error) {
    console.error('Error in updateInventory:', error);

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }

    sendErrorResponse(res, 500, 'خطأ في تحديث سجل الجرد', error);
  }
};

// Delete inventory record
exports.deleteInventory = async (req, res) => {
  try {
    const { id } = req.params;

    const inventory = await Inventory.findByPk(id);

    if (!inventory) {
      return sendErrorResponse(res, 404, 'سجل الجرد غير موجود');
    }

    await inventory.destroy();

    sendSuccessResponse(res, 200, 'تم حذف سجل الجرد بنجاح');
  } catch (error) {
    console.error('Error in deleteInventory:', error);
    sendErrorResponse(res, 500, 'خطأ في حذف سجل الجرد', error);
  }
};

// Get inventory statistics
exports.getInventoryStats = async (req, res) => {
  try {
    const totalRecords = await Inventory.count();
    const totalQuantity = await Inventory.sum('quantity') || 0;
    const outOfStockCount = await Inventory.count({
      where: { quantity: 0 }
    });

    const lowStockCount = await Inventory.count({
      where: {
        [Op.and]: [
          { minimum_quantity: { [Op.ne]: null } },
          { [Op.where]: { [Op.col]: 'quantity' }, [Op.lte]: { [Op.col]: 'minimum_quantity' } }
        ]
      }
    });

    // Get warehouse with most products
    const warehouseStats = await Inventory.findAll({
      attributes: [
        'warehouse_id',
        [Inventory.sequelize.fn('COUNT', Inventory.sequelize.col('id')), 'product_count'],
        [Inventory.sequelize.fn('SUM', Inventory.sequelize.col('quantity')), 'total_quantity']
      ],
      include: [
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['name', 'code']
        }
      ],
      group: ['warehouse_id', 'warehouse.id'],
      order: [[Inventory.sequelize.fn('SUM', Inventory.sequelize.col('quantity')), 'DESC']],
      limit: 5
    });

    // Get products with lowest stock
    const lowStockProducts = await Inventory.findAll({
      where: {
        quantity: { [Op.gt]: 0 }
      },
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['name', 'code']
        },
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['name', 'code']
        }
      ],
      order: [['quantity', 'ASC']],
      limit: 5
    });

    const stats = {
      totalRecords,
      totalQuantity,
      outOfStockCount,
      lowStockCount,
      warehouseStats,
      lowStockProducts
    };

    sendSuccessResponse(res, 200, 'تم جلب إحصائيات الجرد بنجاح', { stats });
  } catch (error) {
    console.error('Error in getInventoryStats:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب إحصائيات الجرد', error);
  }
};
