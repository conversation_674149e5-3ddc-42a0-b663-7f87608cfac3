'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Supplier extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define association here
      Supplier.hasMany(models.Product, {
        foreignKey: 'supplier_id',
        as: 'products'
      });
    }

    // Instance method to get supplier summary
    getSupplierSummary() {
      return {
        id: this.id,
        name: this.name,
        contact_name: this.contact_name,
        phone: this.phone,
        email: this.email
      };
    }

    // Instance method to get full contact info
    getContactInfo() {
      return {
        name: this.name,
        contact_name: this.contact_name,
        phone: this.phone,
        email: this.email,
        address: this.address
      };
    }

    // Static method to find suppliers by name
    static async findByName(name) {
      return await this.findAll({
        where: {
          name: {
            [sequelize.Sequelize.Op.iLike]: `%${name}%`
          }
        }
      });
    }

    // Static method to find suppliers with products
    static async findWithProducts() {
      return await this.findAll({
        include: [{
          model: sequelize.models.Product,
          as: 'products'
        }]
      });
    }
  }

  Supplier.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'اسم المورد مطلوب'
        },
        len: {
          args: [2, 255],
          msg: 'اسم المورد يجب أن يكون بين 2 و 255 حرف'
        }
      }
    },
    contact_name: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [2, 255],
          msg: 'اسم مسؤول التواصل يجب أن يكون بين 2 و 255 حرف'
        }
      }
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [7, 20],
          msg: 'رقم الهاتف يجب أن يكون بين 7 و 20 رقم'
        },
        isNumeric: {
          msg: 'رقم الهاتف يجب أن يحتوي على أرقام فقط'
        }
      }
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: {
          msg: 'البريد الإلكتروني يجب أن يكون بصيغة صحيحة'
        }
      }
    },
    tax_number: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: {
        msg: 'الرقم الضريبي يجب أن يكون فريد'
      },
      validate: {
        len: {
          args: [5, 50],
          msg: 'الرقم الضريبي يجب أن يكون بين 5 و 50 حرف'
        }
      }
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Supplier',
    tableName: 'suppliers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['tax_number'],
        where: {
          tax_number: {
            [sequelize.Sequelize.Op.ne]: null
          }
        }
      },
      {
        fields: ['name']
      },
      {
        fields: ['email']
      }
    ]
  });

  return Supplier;
};
