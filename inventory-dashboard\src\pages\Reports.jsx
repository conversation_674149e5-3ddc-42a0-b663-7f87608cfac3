import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import Card from '../components/Common/Card';
import Loading from '../components/Common/Loading';
import ErrorMessage from '../components/Common/ErrorMessage';
import { reportService } from '../services/reportService';
import {
  ChartBarIcon,
  DocumentChartBarIcon,
  TrendingUpIcon,
  CubeIcon
} from '@heroicons/react/24/outline';

const Reports = () => {
  const [reportData, setReportData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  const fetchReportData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await reportService.getDashboard();
      if (response.success) {
        setReportData(response.data);
      } else {
        setError('فشل في تحميل بيانات التقارير');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
      console.error('Reports error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReportData();
  }, []);

  if (loading) {
    return (
      <Layout title="التقارير">
        <Loading size="large" text="جاري تحميل التقارير..." />
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="التقارير">
        <ErrorMessage message={error} onRetry={fetchReportData} />
      </Layout>
    );
  }

  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: ChartBarIcon },
    { id: 'sales', name: 'تقارير المبيعات', icon: TrendingUpIcon },
    { id: 'products', name: 'تقارير المنتجات', icon: CubeIcon },
    { id: 'inventory', name: 'تقارير المخزون', icon: DocumentChartBarIcon }
  ];

  const kpis = reportData?.kpis || {};
  const salesTrend = reportData?.sales_trend || [];
  const topProducts = reportData?.top_products || [];

  return (
    <Layout title="التقارير">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900">التقارير والإحصائيات</h2>
          <p className="mt-1 text-sm text-gray-600">
            تقارير شاملة عن أداء النظام والمبيعات والمخزون
          </p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 space-x-reverse">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-5 w-5 ml-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Financial Summary */}
            <Card title="الملخص المالي">
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">إجمالي المبيعات</span>
                  <span className="text-lg font-bold text-green-600">
                    {(kpis.financial?.totalRevenue || 0).toLocaleString()} ر.س
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">إجمالي المشتريات</span>
                  <span className="text-lg font-bold text-orange-600">
                    {(kpis.financial?.totalPurchases || 0).toLocaleString()} ر.س
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">صافي الربح</span>
                  <span className={`text-lg font-bold ${
                    (kpis.financial?.grossProfit || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {(kpis.financial?.grossProfit || 0).toLocaleString()} ر.س
                  </span>
                </div>
              </div>
            </Card>

            {/* Inventory Summary */}
            <Card title="ملخص المخزون">
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">إجمالي المنتجات</span>
                  <span className="text-lg font-bold text-blue-600">
                    {kpis.inventory?.totalProducts || 0}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">منتجات منخفضة المخزون</span>
                  <span className="text-lg font-bold text-yellow-600">
                    {kpis.inventory?.lowStockProducts || 0}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">منتجات نافدة المخزون</span>
                  <span className="text-lg font-bold text-red-600">
                    {kpis.inventory?.outOfStockProducts || 0}
                  </span>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'sales' && (
          <div className="space-y-6">
            <Card title="اتجاه المبيعات - آخر 6 أشهر">
              {salesTrend.length > 0 ? (
                <div className="space-y-4">
                  {salesTrend.map((trend, index) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900">{trend.month}</h4>
                        <p className="text-sm text-gray-600">عدد الطلبات: {trend.orders_count}</p>
                      </div>
                      <div className="text-left">
                        <p className="text-lg font-bold text-green-600">
                          {trend.total_revenue?.toLocaleString()} ر.س
                        </p>
                        <p className="text-sm text-gray-600">
                          متوسط الطلب: {trend.average_order_value?.toLocaleString()} ر.س
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">لا توجد بيانات مبيعات متاحة</p>
              )}
            </Card>
          </div>
        )}

        {activeTab === 'products' && (
          <div className="space-y-6">
            <Card title="المنتجات الأكثر مبيعاً">
              {topProducts.length > 0 ? (
                <div className="space-y-4">
                  {topProducts.map((product, index) => (
                    <div key={product.product_id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <span className="flex items-center justify-center w-8 h-8 bg-primary-600 text-white text-sm font-bold rounded-full ml-3">
                          {index + 1}
                        </span>
                        <div>
                          <h4 className="font-medium text-gray-900">{product.product_name}</h4>
                          <p className="text-sm text-gray-600">{product.product_code}</p>
                        </div>
                      </div>
                      <div className="text-left">
                        <p className="font-bold text-gray-900">
                          {product.total_delivered} {product.unit}
                        </p>
                        <p className="text-sm text-green-600">
                          {product.total_revenue?.toLocaleString()} ر.س
                        </p>
                        <p className="text-xs text-gray-500">
                          معدل التسليم: {product.delivery_rate}%
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">لا توجد بيانات منتجات متاحة</p>
              )}
            </Card>
          </div>
        )}

        {activeTab === 'inventory' && (
          <div className="space-y-6">
            <Card title="تقرير حالة المخزون">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-6 bg-green-50 rounded-lg">
                  <div className="text-3xl font-bold text-green-600">
                    {kpis.inventory?.totalProducts || 0}
                  </div>
                  <div className="text-sm text-gray-600 mt-2">إجمالي المنتجات</div>
                </div>
                <div className="text-center p-6 bg-yellow-50 rounded-lg">
                  <div className="text-3xl font-bold text-yellow-600">
                    {kpis.inventory?.lowStockProducts || 0}
                  </div>
                  <div className="text-sm text-gray-600 mt-2">مخزون منخفض</div>
                </div>
                <div className="text-center p-6 bg-red-50 rounded-lg">
                  <div className="text-3xl font-bold text-red-600">
                    {kpis.inventory?.outOfStockProducts || 0}
                  </div>
                  <div className="text-sm text-gray-600 mt-2">نافد المخزون</div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Reports;
